import os
import pandas as pd
import numpy as np
import matplotlib
# 设置非交互式后端，避免Qt错误
matplotlib.use('Agg')
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 不使用seaborn-whitegrid样式，改用基本样式设置
plt.style.use('default')
# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']  # 中文支持
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.dpi'] = 300
plt.rcParams['savefig.dpi'] = 300

# 创建论文图表目录结构
PAPER_DIR = 'Q1_paper_figures'
os.makedirs(PAPER_DIR, exist_ok=True)

# 按分析顺序创建子目录
LONG_TERM_DIR = os.path.join(PAPER_DIR, '1_长周期分析')
SHORT_TERM_DIR = os.path.join(PAPER_DIR, '2_短周期分析')
DEVIATION_DIR = os.path.join(PAPER_DIR, '3_偏差诊断')
COMPARISON_DIR = os.path.join(PAPER_DIR, '4_站点对比')

for directory in [LONG_TERM_DIR, SHORT_TERM_DIR, DEVIATION_DIR, COMPARISON_DIR]:
    os.makedirs(directory, exist_ok=True)

# 读取站点数据
def load_data(station_id):
    file_path = f'dataset/station{station_id:02d}.csv'
    df = pd.read_csv(file_path)
    df['date_time'] = pd.to_datetime(df['date_time'])
    df['date'] = df['date_time'].dt.date
    df['hour'] = df['date_time'].dt.hour
    df['minute'] = df['date_time'].dt.minute
    df['time_of_day'] = df['hour'] + df['minute']/60
    df['month'] = df['date_time'].dt.month
    df['year'] = df['date_time'].dt.year
    df['day'] = df['date_time'].dt.day
    df['dayofyear'] = df['date_time'].dt.dayofyear
    return df

# 计算理论功率
def calculate_theoretical_power(df, capacity=None):
    if capacity is None:
        capacity = df['power'].max() * 1.2
    
    efficiency = 0.85
    df['theoretical_power'] = df['nwp_globalirrad'] * capacity / 1000 * efficiency
    df['relative_efficiency'] = np.where(df['theoretical_power'] > 0, 
                                         df['power'] / df['theoretical_power'], 
                                         np.nan)
    return df, capacity

# 检查站点数据时长
def check_data_duration(stations_data):
    duration_info = {}
    
    for station_id, df in stations_data.items():
        min_date = df['date_time'].min()
        max_date = df['date_time'].max()
        duration_days = (max_date - min_date).days
        
        duration_info[station_id] = {
            'min_date': min_date,
            'max_date': max_date,
            'duration_days': duration_days,
            'has_full_year': duration_days >= 365
        }
        
    return duration_info

# 选择最佳站点用于分析
def select_stations_for_analysis(duration_info):
    # 优先选择有完整一年数据的站点
    full_year_stations = [id for id, info in duration_info.items() if info['has_full_year']]
    
    if len(full_year_stations) >= 3:
        # 如果有3个以上站点有完整一年数据，选择其中的3个
        selected_for_seasonal = sorted(full_year_stations)[:3]
    else:
        # 否则选择持续时间最长的3个站点
        sorted_by_duration = sorted(duration_info.items(), 
                                   key=lambda x: x[1]['duration_days'], 
                                   reverse=True)
        selected_for_seasonal = [id for id, _ in sorted_by_duration[:3]]
    
    # 为其他分析选择代表性站点
    if len(full_year_stations) >= 2:
        selected_for_daily = full_year_stations[:2]
    else:
        selected_for_daily = list(duration_info.keys())[:2]
        
    if len(full_year_stations) >= 4:
        selected_for_efficiency = full_year_stations[:4]
    else:
        selected_for_efficiency = list(duration_info.keys())[:4]
        
    return {
        'seasonal': selected_for_seasonal,
        'daily': selected_for_daily,
        'efficiency': selected_for_efficiency,
        'ramprate': selected_for_daily  # 使用与日内分析相同的站点
    }

# 加载所有站点数据
def load_all_stations():
    stations_data = {}
    for i in range(10):
        try:
            df = load_data(i)
            stations_data[i] = df
            print(f"成功加载站点{i}数据，形状: {df.shape}")
        except Exception as e:
            print(f"加载站点{i}数据失败: {e}")
    return stations_data

# 1. 长周期(季节性)分析
def plot_seasonal_comparison(stations_data, station_ids):
    print(f"绘制季节性趋势对比图 (站点 {station_ids})")
    plt.figure(figsize=(12, 7))
    
    # 确保图例在最佳位置
    capacity_values = []
    
    for station_id in station_ids:
        try:
            df = stations_data[station_id]
            
            # 确保数据按时间排序
            df = df.sort_values('date_time')
            
            # 按日期分组计算平均功率
            daily_avg = df.groupby(pd.to_datetime(df['date']))['power'].mean()
            
            # 获取容量
            df_with_theory, capacity = calculate_theoretical_power(df)
            capacity_values.append(capacity)
            
            # 7天移动平均
            ma7 = daily_avg.rolling(window=7, center=True).mean()
            
            # 标准化为容量百分比
            normalized = ma7 / capacity * 100
            
            plt.plot(normalized.index, normalized, 
                     label=f'站点{station_id} ({capacity:.1f}MW)', 
                     linewidth=2)
        except Exception as e:
            print(f"  处理站点{station_id}时出错: {e}")
    
    plt.title('不同光伏电站的季节性发电特性', fontsize=14)
    plt.xlabel('日期', fontsize=12)
    plt.ylabel('装机容量百分比 (%)', fontsize=12)
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.legend(fontsize=10, loc='best')
    plt.tight_layout()
    
    try:
        plt.savefig(os.path.join(LONG_TERM_DIR, '季节性趋势对比.png'))
        print("  图表已保存")
    except Exception as e:
        print(f"  保存图表失败: {e}")
    plt.close()
    
    # 绘制月度箱型图 - 选择数据最丰富的站点
    best_station_id = station_ids[capacity_values.index(max(capacity_values))]
    plot_monthly_boxplot(stations_data[best_station_id], best_station_id)
    
def plot_monthly_boxplot(df, station_id):
    print(f"绘制月度发电量箱型图 (站点 {station_id})")
    try:
        # 计算装机容量
        _, capacity = calculate_theoretical_power(df)
        
        # 创建新的简单图表 - 月度平均发电量柱状图与折线图组合
        plt.figure(figsize=(12, 6))
        
        # 按月分组计算平均值、最大值和最小值
        monthly_stats = df.groupby(df['date_time'].dt.month)['power'].agg(['mean', 'max', 'min']).reset_index()
        monthly_stats['power_percent_mean'] = monthly_stats['mean'] / capacity * 100
        monthly_stats['power_percent_max'] = monthly_stats['max'] / capacity * 100
        monthly_stats['power_percent_min'] = monthly_stats['min'] / capacity * 100
        
        # 月份标签
        month_labels = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
        
        # 绘制柱状图(平均值)
        bars = plt.bar(monthly_stats['date_time'], monthly_stats['power_percent_mean'], 
                color='skyblue', alpha=0.7, width=0.6)
        
        # 绘制折线图(最大值和最小值)
        plt.plot(monthly_stats['date_time'], monthly_stats['power_percent_max'], 'r-o', 
                label='月最大值')
        plt.plot(monthly_stats['date_time'], monthly_stats['power_percent_min'], 'g-^', 
                label='月最小值')
        
        # 添加数值标签
        for bar in bars:
            height = bar.get_height()
            plt.text(bar.get_x() + bar.get_width()/2., height + 1,
                    f'{height:.1f}%', ha='center', va='bottom', fontsize=8)
        
        plt.title(f'站点{station_id} - 月度发电量统计 ({capacity:.1f}MW)', fontsize=14)
        plt.xlabel('月份', fontsize=12)
        plt.ylabel('装机容量百分比 (%)', fontsize=12)
        plt.xticks(range(1, 13), month_labels)
        plt.legend(['月最大值', '月最小值', '月平均值'])
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.tight_layout()
        
        plt.savefig(os.path.join(LONG_TERM_DIR, f'月度发电量统计_站点{station_id}.png'))
        print("  月度发电量统计图已保存")
    except Exception as e:
        print(f"  绘制月度发电量统计图失败: {e}")
    plt.close()
    
    # 为完整性仍保留原来的箱型图，但可以根据需要注释掉
    try:
        plt.figure(figsize=(12, 6))
        
        monthly_data = df.copy()
        monthly_data['month'] = monthly_data['date_time'].dt.month
        
        # 将功率转换为容量百分比
        monthly_data['power_percent'] = monthly_data['power'] / capacity * 100
        
        # 使用seaborn绘制箱型图
        sns.boxplot(x='month', y='power_percent', data=monthly_data)
        
        plt.title(f'站点{station_id} - 月度发电量分布 ({capacity:.1f}MW)', fontsize=14)
        plt.xlabel('月份', fontsize=12)
        plt.ylabel('装机容量百分比 (%)', fontsize=12)
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.tight_layout()
        
        plt.savefig(os.path.join(LONG_TERM_DIR, f'月度发电量箱型图_站点{station_id}.png'))
        print("  月度发电量箱型图已保存")
    except Exception as e:
        print(f"  绘制月度箱型图失败: {e}")
    plt.close()
    
    # 绘制月度平均发电量的季节性变化
    try:
        plt.figure(figsize=(12, 6))
        
        # 创建新的DataFrame进行分析
        temp_df = df.copy()
        temp_df['year'] = temp_df['date_time'].dt.year
        temp_df['month'] = temp_df['date_time'].dt.month
        
        # 按年月分组计算平均值
        monthly_avg = temp_df.groupby(['year', 'month'])['power'].mean().reset_index()
        monthly_avg['year_month'] = monthly_avg['year'].astype(str) + '-' + monthly_avg['month'].astype(str).str.zfill(2)
        monthly_avg['power_percent'] = monthly_avg['power'] / capacity * 100
        
        plt.bar(monthly_avg['year_month'], monthly_avg['power_percent'], color='skyblue')
        plt.title(f'站点{station_id} - 月度平均发电量 ({capacity:.1f}MW)', fontsize=14)
        plt.xlabel('年-月', fontsize=12)
        plt.ylabel('装机容量百分比 (%)', fontsize=12)
        plt.xticks(rotation=45)
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.tight_layout()
        
        plt.savefig(os.path.join(LONG_TERM_DIR, f'月度平均发电量_站点{station_id}.png'))
        print("  月度平均发电量图已保存")
    except Exception as e:
        print(f"  绘制月度平均发电量图失败: {e}")
    plt.close()

# 2. 短周期(日内)分析
def plot_typical_day_patterns(stations_data, station_ids):
    print(f"绘制典型日模式分析图 (站点 {station_ids})")
    
    for station_id in station_ids:
        try:
            df = stations_data[station_id]
            df_with_theory, capacity = calculate_theoretical_power(df)
            
            # 找出完整的日数据
            daily_patterns = []
            dates = []
            weather_condition = []  # 用于存储天气条件分类
            
            for date, group in df.groupby('date'):
                if len(group) >= 96:  # 假设完整一天有96个数据点
                    daily_pattern = group.sort_values('date_time').iloc[:96]['power'].values
                    daily_patterns.append(daily_pattern)
                    dates.append(date)
                    
                    # 根据辐照度推断天气状况
                    avg_irrad = group['nwp_globalirrad'].mean()
                    if avg_irrad < 200:
                        weather = "阴天"
                    elif avg_irrad < 400:
                        weather = "多云"
                    else:
                        weather = "晴天"
                    weather_condition.append(weather)
            
            if len(daily_patterns) > 0:
                daily_patterns = np.array(daily_patterns)
                
                # 按照总发电量归一化
                normalized_patterns = daily_patterns / capacity
                
                # 按天气状况分类
                sunny_idx = [i for i, w in enumerate(weather_condition) if w == "晴天"]
                partly_cloudy_idx = [i for i, w in enumerate(weather_condition) if w == "多云"]
                cloudy_idx = [i for i, w in enumerate(weather_condition) if w == "阴天"]
                
                plt.figure(figsize=(14, 8))
                time_points = np.linspace(0, 24, 96)
                
                if sunny_idx:
                    sunny_patterns = normalized_patterns[sunny_idx]
                    mean_sunny = np.mean(sunny_patterns, axis=0)
                    p10_sunny = np.percentile(sunny_patterns, 10, axis=0)
                    p90_sunny = np.percentile(sunny_patterns, 90, axis=0)
                    plt.plot(time_points, mean_sunny*100, 'r-', label=f'晴天 (n={len(sunny_idx)})', linewidth=2)
                    plt.fill_between(time_points, p10_sunny*100, p90_sunny*100, color='r', alpha=0.2)
                
                if partly_cloudy_idx:
                    partly_patterns = normalized_patterns[partly_cloudy_idx]
                    mean_partly = np.mean(partly_patterns, axis=0)
                    p10_partly = np.percentile(partly_patterns, 10, axis=0)
                    p90_partly = np.percentile(partly_patterns, 90, axis=0)
                    plt.plot(time_points, mean_partly*100, 'g-', label=f'多云 (n={len(partly_cloudy_idx)})', linewidth=2)
                    plt.fill_between(time_points, p10_partly*100, p90_partly*100, color='g', alpha=0.2)
                
                if cloudy_idx:
                    cloudy_patterns = normalized_patterns[cloudy_idx]
                    mean_cloudy = np.mean(cloudy_patterns, axis=0)
                    p10_cloudy = np.percentile(cloudy_patterns, 10, axis=0)
                    p90_cloudy = np.percentile(cloudy_patterns, 90, axis=0)
                    plt.plot(time_points, mean_cloudy*100, 'b-', label=f'阴天 (n={len(cloudy_idx)})', linewidth=2)
                    plt.fill_between(time_points, p10_cloudy*100, p90_cloudy*100, color='b', alpha=0.2)
                
                plt.title(f'站点{station_id} - 典型日功率曲线模式 (装机容量 {capacity:.1f}MW)', fontsize=14)
                plt.xlabel('时间 (小时)', fontsize=12)
                plt.ylabel('装机容量百分比 (%)', fontsize=12)
                plt.grid(True, linestyle='--', alpha=0.7)
                plt.legend(fontsize=10, loc='best')
                plt.tight_layout()
                
                plt.savefig(os.path.join(SHORT_TERM_DIR, f'典型日功率曲线_站点{station_id}.png'))
                print(f"  站点{station_id}的典型日功率曲线图已保存")
                plt.close()
                
                # 额外绘制功率波动量的小提琴图
                plt.figure(figsize=(10, 6))
                
                # 计算每天的功率变化率标准差作为波动指标
                variation_sunny = [np.std(np.diff(pattern)) for pattern in sunny_patterns] if sunny_idx else []
                variation_partly = [np.std(np.diff(pattern)) for pattern in partly_patterns] if partly_cloudy_idx else []
                variation_cloudy = [np.std(np.diff(pattern)) for pattern in cloudy_patterns] if cloudy_idx else []
                
                data = []
                labels = []
                
                if variation_sunny:
                    data.append(np.array(variation_sunny) / capacity * 100)
                    labels.extend(['晴天'] * len(variation_sunny))
                
                if variation_partly:
                    data.append(np.array(variation_partly) / capacity * 100)
                    labels.extend(['多云'] * len(variation_partly))
                
                if variation_cloudy:
                    data.append(np.array(variation_cloudy) / capacity * 100)
                    labels.extend(['阴天'] * len(variation_cloudy))
                
                if data:
                    df_variation = pd.DataFrame({'天气类型': labels, '功率波动': np.concatenate(data)})
                    sns.violinplot(x='天气类型', y='功率波动', data=df_variation)
                    plt.title(f'站点{station_id} - 不同天气条件下的功率波动分布', fontsize=14)
                    plt.xlabel('天气类型', fontsize=12)
                    plt.ylabel('功率波动标准差 (% of 装机容量)', fontsize=12)
                    plt.grid(True, linestyle='--', alpha=0.7)
                    plt.tight_layout()
                    
                    plt.savefig(os.path.join(SHORT_TERM_DIR, f'功率波动分布_站点{station_id}.png'))
                    print(f"  站点{station_id}的功率波动分布图已保存")
                plt.close()
            else:
                print(f"  站点{station_id}没有足够的完整日数据")
        except Exception as e:
            print(f"  处理站点{station_id}时出错: {e}") 

# 功率变化率的时间分布特征
def plot_ramprate_time_distribution(stations_data, station_ids):
    print(f"绘制功率变化率时间分布图 (站点 {station_ids})")
    
    for station_id in station_ids:
        try:
            df = stations_data[station_id]
            df_with_theory, capacity = calculate_theoretical_power(df)
            
            # 计算功率变化率
            df_with_theory['power_diff'] = df_with_theory.groupby('date')['power'].diff()
            df_with_theory['ramp_rate'] = df_with_theory['power_diff'] / 0.25  # 假设15分钟间隔
            df_with_theory['ramp_rate_pct'] = df_with_theory['ramp_rate'] / capacity * 100  # 转换为百分比
            
            # 按小时统计ramp rate的绝对值
            df_with_theory['abs_ramp_rate_pct'] = df_with_theory['ramp_rate_pct'].abs()
            hourly_ramp = df_with_theory.groupby('hour')['abs_ramp_rate_pct'].agg(['mean', 'std', 'max']).reset_index()
            
            plt.figure(figsize=(12, 6))
            plt.errorbar(hourly_ramp['hour'], hourly_ramp['mean'], yerr=hourly_ramp['std'], 
                         fmt='o-', capsize=5, label='平均值±标准差')
            plt.plot(hourly_ramp['hour'], hourly_ramp['max'], 'r--', label='最大值')
            plt.title(f'站点{station_id} - 功率变化率的时间分布 ({capacity:.1f}MW)', fontsize=14)
            plt.xlabel('小时', fontsize=12)
            plt.ylabel('功率变化率 (% of 装机容量/小时)', fontsize=12)
            plt.grid(True, linestyle='--', alpha=0.7)
            plt.legend(fontsize=10)
            plt.xticks(range(0, 24))
            plt.xlim(-0.5, 23.5)
            plt.tight_layout()
            
            plt.savefig(os.path.join(SHORT_TERM_DIR, f'功率变化率时间分布_站点{station_id}.png'))
            print(f"  站点{station_id}的功率变化率时间分布图已保存")
            plt.close()
            
            # 绘制ramp rate分布直方图
            plt.figure(figsize=(10, 6))
            # 移除无穷大值和NaN并限制范围
            ramp_rate_clean = df_with_theory['ramp_rate_pct'].replace([np.inf, -np.inf], np.nan).dropna()
            # 限制范围，防止极端值影响可视化
            ramp_rate_filtered = ramp_rate_clean[(ramp_rate_clean > -200) & (ramp_rate_clean < 200)]
            
            plt.hist(ramp_rate_filtered, bins=50, alpha=0.7, color='skyblue')
            plt.axvline(x=0, color='r', linestyle='--')
            plt.title(f'站点{station_id} - 功率变化率分布 ({capacity:.1f}MW)', fontsize=14)
            plt.xlabel('功率变化率 (% of 装机容量/小时)', fontsize=12)
            plt.ylabel('频率', fontsize=12)
            plt.grid(True, linestyle='--', alpha=0.7)
            plt.tight_layout()
            
            plt.savefig(os.path.join(SHORT_TERM_DIR, f'功率变化率分布_站点{station_id}.png'))
            print(f"  站点{station_id}的功率变化率分布图已保存")
            plt.close()
        except Exception as e:
            print(f"  处理站点{station_id}时出错: {e}")

# 3. 偏差诊断
def plot_irradiance_efficiency(stations_data, station_ids):
    print(f"绘制辐照度-效率关系分析图 (站点 {station_ids})")
    
    # 单个站点详细分析
    for station_id in station_ids:
        try:
            df = stations_data[station_id]
            df_with_theory, capacity = calculate_theoretical_power(df)
            
            # 按辐照度水平分组计算平均效率
            df_with_theory['irrad_bin'] = pd.cut(df_with_theory['nwp_globalirrad'], 
                                  bins=[0, 100, 200, 300, 400, 500, 600, 700, 800, 900, 1000, np.inf],
                                  labels=['0-100', '100-200', '200-300', '300-400', '400-500', 
                                          '500-600', '600-700', '700-800', '800-900', '900-1000', '1000+'])
            
            irrad_eff = df_with_theory.groupby('irrad_bin')['relative_efficiency'].mean().reset_index()
            
            plt.figure(figsize=(12, 6))
            plt.bar(range(len(irrad_eff)), irrad_eff['relative_efficiency'], color='skyblue')
            plt.axhline(y=1, color='r', linestyle='--')
            plt.title(f'站点{station_id} - 辐照度与效率关系 ({capacity:.1f}MW)', fontsize=14)
            plt.xlabel('辐照度范围 (W/m²)', fontsize=12)
            plt.ylabel('相对效率 (实际/理论)', fontsize=12)
            plt.xticks(range(len(irrad_eff)), irrad_eff['irrad_bin'], rotation=45)
            plt.grid(True, linestyle='--', alpha=0.7)
            plt.tight_layout()
            
            plt.savefig(os.path.join(DEVIATION_DIR, f'辐照度效率关系_站点{station_id}.png'))
            print(f"  站点{station_id}的辐照度效率关系图已保存")
            plt.close()
            
            # 按月分析相对效率的季节性变化
            df_with_theory['year_month'] = df_with_theory['date_time'].dt.strftime('%Y-%m')
            monthly_rel_eff = df_with_theory.groupby('year_month')['relative_efficiency'].mean().reset_index()
            
            plt.figure(figsize=(12, 6))
            plt.bar(monthly_rel_eff['year_month'], monthly_rel_eff['relative_efficiency'], color='skyblue')
            plt.axhline(y=1, color='r', linestyle='--')
            plt.title(f'站点{station_id} - 月平均相对效率 ({capacity:.1f}MW)', fontsize=14)
            plt.xlabel('年-月', fontsize=12)
            plt.ylabel('相对效率 (实际/理论)', fontsize=12)
            plt.xticks(rotation=45)
            plt.grid(True, linestyle='--', alpha=0.7)
            plt.tight_layout()
            
            plt.savefig(os.path.join(DEVIATION_DIR, f'月平均相对效率_站点{station_id}.png'))
            print(f"  站点{station_id}的月平均相对效率图已保存")
            plt.close()
        except Exception as e:
            print(f"  处理站点{station_id}时出错: {e}")
    
    # 综合对比图
    plt.figure(figsize=(14, 10))
    
    for i, station_id in enumerate(station_ids):
        try:
            df = stations_data[station_id]
            df_with_theory, capacity = calculate_theoretical_power(df)
            
            # 按辐照度水平分组计算平均效率
            df_with_theory['irrad_bin'] = pd.cut(df_with_theory['nwp_globalirrad'], 
                                  bins=[0, 100, 200, 300, 400, 500, 600, 700, 800, 900, 1000, np.inf],
                                  labels=['0-100', '100-200', '200-300', '300-400', '400-500', 
                                          '500-600', '600-700', '700-800', '800-900', '900-1000', '1000+'])
            
            irrad_eff = df_with_theory.groupby('irrad_bin')['relative_efficiency'].mean().reset_index()
            
            plt.subplot(2, 2, i+1)
            plt.bar(range(len(irrad_eff)), irrad_eff['relative_efficiency'], color='skyblue')
            plt.axhline(y=1, color='r', linestyle='--')
            plt.title(f'站点{station_id} ({capacity:.1f}MW)', fontsize=12)
            plt.xlabel('辐照度范围 (W/m²)', fontsize=10)
            plt.ylabel('相对效率', fontsize=10)
            plt.xticks(range(len(irrad_eff)), irrad_eff['irrad_bin'], rotation=45, fontsize=8)
            plt.grid(True, linestyle='--', alpha=0.7)
            plt.tight_layout()
        except Exception as e:
            print(f"  处理站点{station_id}时出错: {e}")
    
    plt.suptitle('不同站点在各辐照度水平下的相对效率对比', fontsize=16, y=1.02)
    plt.tight_layout()
    
    plt.savefig(os.path.join(DEVIATION_DIR, '站点辐照度效率对比.png'))
    print("  站点辐照度效率对比图已保存")
    plt.close()

# 4. 站点对比分析
def analyze_station_comparisons(stations_data):
    print("分析站点对比特性...")
    
    # 计算关键指标
    results = []
    
    for station_id, df in stations_data.items():
        try:
            # 计算容量因子 (CF)
            df_with_theory, capacity = calculate_theoretical_power(df)
            cf = df['power'].mean() / capacity if capacity > 0 else 0
            
            # 计算最大/最小日CF
            daily_power = df.groupby('date')['power'].mean()
            if len(daily_power) > 0:
                max_daily_cf = daily_power.max() / capacity if capacity > 0 else 0
                min_daily_cf = (daily_power[daily_power > 0].min() if len(daily_power[daily_power > 0]) > 0 else 0) / capacity if capacity > 0 else 0
                
                # 计算最大15分钟ramp
                df['power_diff'] = df.groupby('date')['power'].diff()
                max_ramp = df['power_diff'].abs().max()
                
                results.append({
                    'station_id': station_id,
                    'capacity_factor': cf,
                    'max_daily_cf': max_daily_cf,
                    'min_daily_cf': min_daily_cf,
                    'max_15min_ramp': max_ramp,
                    'max_15min_ramp_pct': max_ramp / capacity * 100 if capacity > 0 else 0,  # 转为百分比
                    'data_days': len(daily_power),
                    'capacity_estimate': capacity
                })
        except Exception as e:
            print(f"  站点{station_id}的对比分析失败: {e}")
    
    # 创建汇总DataFrame
    summary_df = pd.DataFrame(results)
    
    # 绘制容量因子比较图
    plt.figure(figsize=(12, 6))
    bars = plt.bar(summary_df['station_id'], summary_df['capacity_factor'] * 100, color='skyblue')
    
    # 在柱状图上添加数值标签
    for bar in bars:
        height = bar.get_height()
        plt.text(bar.get_x() + bar.get_width()/2., height + 0.3,
                 f'{height:.1f}%', ha='center', va='bottom', fontsize=9)
    
    plt.title('各站点容量因子(CF)对比', fontsize=14)
    plt.xlabel('站点ID', fontsize=12)
    plt.ylabel('容量因子 (%)', fontsize=12)
    plt.xticks(summary_df['station_id'])
    plt.grid(axis='y', linestyle='--', alpha=0.7)
    plt.tight_layout()
    
    plt.savefig(os.path.join(COMPARISON_DIR, '容量因子对比.png'))
    print("  容量因子对比图已保存")
    plt.close()
    
    # 绘制最大ramp比较图
    plt.figure(figsize=(12, 6))
    bars = plt.bar(summary_df['station_id'], summary_df['max_15min_ramp_pct'], color='salmon')
    
    # 在柱状图上添加数值标签
    for bar in bars:
        height = bar.get_height()
        plt.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                 f'{height:.1f}%', ha='center', va='bottom', fontsize=9)
    
    plt.title('各站点最大15分钟功率变化率对比', fontsize=14)
    plt.xlabel('站点ID', fontsize=12)
    plt.ylabel('最大功率变化率 (%)', fontsize=12)
    plt.xticks(summary_df['station_id'])
    plt.grid(axis='y', linestyle='--', alpha=0.7)
    plt.tight_layout()
    
    plt.savefig(os.path.join(COMPARISON_DIR, '最大功率变化率对比.png'))
    print("  最大功率变化率对比图已保存")
    plt.close()
    
    # 保存汇总表
    summary_df.to_csv(os.path.join(COMPARISON_DIR, '站点对比汇总.csv'), index=False)
    print("  站点对比汇总表已保存")
    
    # 绘制综合对比图：容量vs容量因子vs变化率
    plt.figure(figsize=(12, 8))
    
    # 创建散点图，大小表示装机容量，颜色表示最大变化率
    scatter = plt.scatter(summary_df['station_id'], 
                          summary_df['capacity_factor'] * 100,
                          s=summary_df['capacity_estimate'] * 5,  # 点大小与装机容量成正比
                          c=summary_df['max_15min_ramp_pct'],    # 颜色与最大变化率对应
                          cmap='viridis',
                          alpha=0.7)
    
    # 添加站点标签
    for i, row in summary_df.iterrows():
        plt.annotate(f"站点{int(row['station_id'])}",
                    (row['station_id'], row['capacity_factor'] * 100),
                    textcoords="offset points",
                    xytext=(0, 10),
                    ha='center')
    
    plt.colorbar(scatter, label='最大15分钟功率变化率 (%)')
    plt.title('光伏电站综合特性对比', fontsize=14)
    plt.xlabel('站点ID', fontsize=12)
    plt.ylabel('容量因子 (%)', fontsize=12)
    plt.xticks(summary_df['station_id'])
    plt.grid(True, linestyle='--', alpha=0.7)
    
    # 添加图例解释散点大小
    sizes = [10, 20, 30]
    labels = ['10MW', '20MW', '30MW']
    
    # 添加散点大小图例
    for size, label in zip(sizes, labels):
        plt.scatter([], [], s=size*5, c='gray', alpha=0.7, label=label)
    
    plt.legend(title='装机容量', loc='best')
    plt.tight_layout()
    
    plt.savefig(os.path.join(COMPARISON_DIR, '站点综合特性对比.png'))
    print("  站点综合特性对比图已保存")
    plt.close()
    
    return summary_df

# 主函数
def main():
    print("开始绘制光伏电站发电特性论文图表...")
    
    # 加载所有站点数据
    stations_data = load_all_stations()
    
    # 检查数据时长
    duration_info = check_data_duration(stations_data)
    for station_id, info in duration_info.items():
        print(f"站点{station_id}数据时长: {info['duration_days']}天 " +
              f"({info['min_date'].date()} 至 {info['max_date'].date()}), " +
              f"{'有' if info['has_full_year'] else '无'}完整一年数据")
    
    # 选择最佳站点用于各类分析
    selected_stations = select_stations_for_analysis(duration_info)
    print("\n选择的分析站点:")
    for analysis_type, station_ids in selected_stations.items():
        print(f"  {analysis_type}分析: 站点{station_ids}")
    
    # 1. 长周期(季节性)分析
    print("\n======= 1. 长周期(季节性)分析 =======")
    plot_seasonal_comparison(stations_data, selected_stations['seasonal'])
    
    # 2. 短周期(日内)分析
    print("\n======= 2. 短周期(日内)分析 =======")
    plot_typical_day_patterns(stations_data, selected_stations['daily'])
    plot_ramprate_time_distribution(stations_data, selected_stations['ramprate'])
    
    # 3. 偏差诊断
    print("\n======= 3. 偏差诊断 =======")
    plot_irradiance_efficiency(stations_data, selected_stations['efficiency'])
    
    # 4. 站点对比分析
    print("\n======= 4. 站点对比分析 =======")
    summary_df = analyze_station_comparisons(stations_data)
    
    print("\n所有图表已按分析顺序保存在对应子目录中")
    print(f"  1. 长周期分析: {LONG_TERM_DIR}")
    print(f"  2. 短周期分析: {SHORT_TERM_DIR}")
    print(f"  3. 偏差诊断: {DEVIATION_DIR}")
    print(f"  4. 站点对比: {COMPARISON_DIR}")

if __name__ == "__main__":
    main() 