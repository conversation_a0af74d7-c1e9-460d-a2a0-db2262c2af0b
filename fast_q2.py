import os
import pandas as pd
import numpy as np
import matplotlib
matplotlib.use('Agg')  # 非交互式后端
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')
from sklearn.metrics import mean_absolute_error
from tqdm import tqdm
import pickle
import time
import lightgbm as lgb
from xgboost import XGBRegressor
from sklearn.preprocessing import StandardScaler

# 设置中文字体和图表风格
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.dpi'] = 300
plt.rcParams['savefig.dpi'] = 300

# 创建结果目录
RESULTS_DIR = 'Q2_fast_results'
os.makedirs(RESULTS_DIR, exist_ok=True)

# 数据处理函数
def load_station_data(station_id):
    """加载站点数据"""
    file_path = f'dataset/station{station_id:02d}.csv'
    df = pd.read_csv(file_path)
    df['date_time'] = pd.to_datetime(df['date_time'])
    
    # 添加时间相关特征
    df['hour'] = df['date_time'].dt.hour
    df['minute'] = df['date_time'].dt.minute
    df['day'] = df['date_time'].dt.day
    df['month'] = df['date_time'].dt.month
    df['year'] = df['date_time'].dt.year
    df['dayofweek'] = df['date_time'].dt.dayofweek
    df['date'] = df['date_time'].dt.date
    
    # 添加时间段标记（96个时间点）
    df['time_point'] = df['hour'] * 4 + df['minute'] // 15

    # 添加白昼标记 (6点到18点之间视为白昼)
    df['is_daytime'] = ((df['hour'] >= 6) & (df['hour'] <= 18)).astype(int)
    
    return df

def prepare_features(df, lag_days=[1, 2, 3], window_sizes=[12, 24, 96]):
    """准备预测特征 - 简化版"""
    # 复制数据，避免修改原始数据
    df_features = df.copy()
    
    # 1. 滞后功率特征
    for day in lag_days:
        lag_points = day * 96  # 每天96个时间点
        df_features[f'power_lag_{day}d'] = df_features['power'].shift(lag_points)
    
    # 2. 滑动统计特征
    for window in window_sizes:
        # 滑动平均
        df_features[f'power_mean_{window}'] = df_features['power'].rolling(window=window, min_periods=1).mean()
        # 滑动标准差
        df_features[f'power_std_{window}'] = df_features['power'].rolling(window=window, min_periods=1).std()
    
    # 3. 时间特征的周期性表示
    df_features['hour_sin'] = np.sin(2 * np.pi * df_features['hour'] / 24)
    df_features['hour_cos'] = np.cos(2 * np.pi * df_features['hour'] / 24)
    df_features['month_sin'] = np.sin(2 * np.pi * df_features['month'] / 12)
    df_features['month_cos'] = np.cos(2 * np.pi * df_features['month'] / 12)
    df_features['dayofweek_sin'] = np.sin(2 * np.pi * df_features['dayofweek'] / 7)
    df_features['dayofweek_cos'] = np.cos(2 * np.pi * df_features['dayofweek'] / 7)
    
    # 4. 使用NWP气象数据特征
    for col in df.columns:
        if 'nwp_' in col:
            # 添加滞后特征
            df_features[f'{col}_lag_1d'] = df_features[col].shift(96)
            
    # 5. 填充缺失值，使用前后值的中位数
    for col in df_features.columns:
        if df_features[col].dtype in [np.float64, np.float32, np.int64, np.int32]:
            df_features[col] = df_features[col].fillna(df_features[col].median())
    
    return df_features

def get_train_test_split(df, test_months=[2, 5, 8, 11]):
    """按照题目要求划分训练集和测试集
    
    训练集：除2/5/8/11月最后一周外的数据
    测试集：2/5/8/11月最后一周数据
    """
    # 先按月份筛选出测试月份
    test_month_df = df[df['month'].isin(test_months)]
    
    # 对于每个测试月份，找出最后一周
    test_indices = []
    for month in test_months:
        month_df = df[df['month'] == month]
        if len(month_df) == 0:
            continue
            
        # 获取该月最大日期
        max_date = month_df['date'].max()
        
        # 获取最大日期前7天的数据作为测试集
        test_dates = [(max_date - timedelta(days=i)) for i in range(7)]
        month_test_indices = df[df['date'].isin(test_dates)].index
        test_indices.extend(month_test_indices)
    
    # 剩余数据作为训练集
    train_indices = df.index.difference(test_indices)
    
    return train_indices, test_indices

def evaluate_models(y_true, predictions, is_daytime=None):
    """评估不同模型的预测效果，可选择仅评估白昼时段"""
    results = {}
    
    for model_name, y_pred in predictions.items():
        # 如果提供了白昼标记，则只评估白昼时间段
        if is_daytime is not None:
            mask = is_daytime == 1
            y_true_eval = y_true[mask]
            y_pred_eval = y_pred[mask]
        else:
            y_true_eval = y_true
            y_pred_eval = y_pred
        
        # 计算平均绝对误差 (MAE)
        mae = np.mean(np.abs(y_true_eval - y_pred_eval))
        
        # 计算均方根误差 (RMSE)
        rmse = np.sqrt(np.mean((y_true_eval - y_pred_eval) ** 2))
        
        # 计算归一化均方根误差 (nRMSE)
        nrmse = rmse / np.mean(y_true_eval)
        
        # 计算对称平均绝对百分比误差 (SMAPE)
        smape = 100 * np.mean(2 * np.abs(y_pred_eval - y_true_eval) / (np.abs(y_pred_eval) + np.abs(y_true_eval) + 1e-8))
        
        results[model_name] = {
            'MAE': mae,
            'RMSE': rmse,
            'nRMSE': nrmse,
            'SMAPE': smape
        }
    
    # 转换为DataFrame以便于展示
    results_df = pd.DataFrame(results).T
    return results_df

def persistence_model(train_data, test_dates, time_points=96):
    """
    实现Persistence模型（基准线）
    使用前一天同一时刻的功率作为预测值
    """
    print("运行Persistence基准模型...")
    predictions = []
    
    # 对每个测试日期进行预测
    for test_date in test_dates:
        # 获取前一天的日期
        prev_day = test_date - timedelta(days=1)
        
        # 获取前一天的功率值
        prev_day_powers = train_data[train_data['date'] == prev_day]['power'].values
        
        if len(prev_day_powers) == time_points:
            # 如果前一天有完整的数据，直接使用
            predictions.extend(prev_day_powers)
        else:
            # 如果前一天数据不完整，使用最近的有效日期
            for day_offset in range(2, 8):  # 尝试前2~7天
                prev_day = test_date - timedelta(days=day_offset)
                prev_day_powers = train_data[train_data['date'] == prev_day]['power'].values
                if len(prev_day_powers) == time_points:
                    predictions.extend(prev_day_powers)
                    break
            else:
                # 如果前7天都没有完整数据，使用训练集中同时刻的平均值
                for time_point in range(time_points):
                    time_point_avg = train_data[train_data['time_point'] == time_point]['power'].mean()
                    predictions.append(time_point_avg)
    
    return np.array(predictions)

def fast_lightgbm_model(X_train, y_train, X_test):
    """
    使用LightGBM模型进行快速预测 - 简化版本
    """
    print("运行快速LightGBM模型...")
    
    # 去掉非特征列
    drop_cols = ['date_time', 'date', 'time_point']
    feature_cols = [col for col in X_train.columns if col not in drop_cols and col != 'power']
    
    X_train_lgbm = X_train[feature_cols].copy()
    X_test_lgbm = X_test[feature_cols].copy()
    
    # LightGBM参数 - 简化版，提高训练速度
    params = {
        'boosting_type': 'gbdt',
        'objective': 'regression',
        'metric': 'mae',
        'learning_rate': 0.1,
        'num_leaves': 31,
        'max_depth': 6,
        'feature_fraction': 0.8,
        'bagging_fraction': 0.8,
        'bagging_freq': 5,
        'verbose': -1
    }
    
    # 创建数据集
    train_data = lgb.Dataset(X_train_lgbm, label=y_train)
    
    # 训练模型 - 减少迭代次数，提高速度
    model = lgb.train(
        params,
        train_data,
        num_boost_round=200  # 减少迭代次数
    )
    
    # 预测
    predictions = model.predict(X_test_lgbm)
    
    # 获取特征重要性
    feature_importance = pd.DataFrame({
        'Feature': feature_cols,
        'Importance': model.feature_importance()
    }).sort_values(by='Importance', ascending=False)
    
    # 保存特征重要性
    feature_importance.to_csv(os.path.join(RESULTS_DIR, 'lightgbm_feature_importance.csv'), index=False)
    
    # 可视化前15个重要特征
    plt.figure(figsize=(10, 6))
    sns.barplot(x='Importance', y='Feature', data=feature_importance.head(15))
    plt.title('LightGBM 特征重要性 (Top 15)')
    plt.tight_layout()
    plt.savefig(os.path.join(RESULTS_DIR, 'lightgbm_feature_importance.png'))
    plt.close()
    
    return predictions, feature_importance

def fast_xgboost_model(X_train, y_train, X_test):
    """
    使用XGBoost模型进行快速预测 - 简化版本
    """
    print("运行快速XGBoost模型...")
    
    # 去掉非特征列
    drop_cols = ['date_time', 'date', 'time_point']
    feature_cols = [col for col in X_train.columns if col not in drop_cols and col != 'power']
    
    X_train_xgb = X_train[feature_cols].copy()
    X_test_xgb = X_test[feature_cols].copy()
    
    # 训练模型 - 使用更快的参数设置
    model = XGBRegressor(
        n_estimators=200,  # 减少树的数量
        max_depth=6,
        learning_rate=0.1,
        subsample=0.8,
        colsample_bytree=0.8,
        objective='reg:squarederror',
        n_jobs=-1  # 使用全部CPU核心
    )
    
    # 训练模型
    model.fit(X_train_xgb, y_train)
    
    # 预测
    predictions = model.predict(X_test_xgb)
    
    # 获取特征重要性
    feature_importance = pd.DataFrame({
        'Feature': feature_cols,
        'Importance': model.feature_importances_
    }).sort_values(by='Importance', ascending=False)
    
    # 保存特征重要性
    feature_importance.to_csv(os.path.join(RESULTS_DIR, 'xgboost_feature_importance.csv'), index=False)
    
    # 可视化前15个重要特征
    plt.figure(figsize=(10, 6))
    sns.barplot(x='Importance', y='Feature', data=feature_importance.head(15))
    plt.title('XGBoost 特征重要性 (Top 15)')
    plt.tight_layout()
    plt.savefig(os.path.join(RESULTS_DIR, 'xgboost_feature_importance.png'))
    plt.close()
    
    return predictions, feature_importance

def plot_prediction_comparison(y_true, predictions, is_daytime, title, save_path):
    """绘制不同模型的预测效果对比"""
    # 将数据和预测转为DataFrame以便处理
    result_df = pd.DataFrame({'True': y_true})
    
    # 添加预测值和白昼标记
    for model_name, y_pred in predictions.items():
        result_df[model_name] = y_pred
    
    result_df['is_daytime'] = is_daytime
    
    # 绘制全部数据对比
    plt.figure(figsize=(15, 6))
    plt.plot(result_df['True'].values, 'k-', label='真实值', linewidth=2)
    
    # 绘制不同模型的预测值
    for model_name in predictions.keys():
        plt.plot(result_df[model_name].values, '--', label=f'{model_name}预测值', linewidth=1.5, alpha=0.8)
    
    # 标注白天区域
    for i in range(len(result_df)):
        if i > 0 and result_df['is_daytime'].iloc[i] != result_df['is_daytime'].iloc[i-1]:
            if result_df['is_daytime'].iloc[i] == 1:
                plt.axvline(x=i, color='gold', linestyle='-', alpha=0.3)
                plt.text(i, result_df['True'].max() * 0.9, "白昼开始", rotation=90, alpha=0.7)
            else:
                plt.axvline(x=i, color='navy', linestyle='-', alpha=0.3)
                plt.text(i, result_df['True'].max() * 0.9, "白昼结束", rotation=90, alpha=0.7)
    
    plt.title(title, fontsize=14)
    plt.xlabel('时间点', fontsize=12)
    plt.ylabel('功率 (MW)', fontsize=12)
    plt.legend(loc='best')
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.tight_layout()
    
    plt.savefig(save_path)
    plt.close()
    
    # 只绘制前两天的数据，以便更清晰地查看
    if len(result_df) > 96*2:
        plt.figure(figsize=(15, 6))
        
        # 提取前两天数据
        first_two_days = result_df.iloc[:96*2]
        plt.plot(first_two_days['True'].values, 'k-', label='真实值', linewidth=2)
        
        for model_name in predictions.keys():
            plt.plot(first_two_days[model_name].values, '--', label=f'{model_name}预测值', linewidth=1.5, alpha=0.8)
        
        # 标注白天区域
        for i in range(len(first_two_days)):
            if i > 0 and first_two_days['is_daytime'].iloc[i] != first_two_days['is_daytime'].iloc[i-1]:
                if first_two_days['is_daytime'].iloc[i] == 1:
                    plt.axvline(x=i, color='gold', linestyle='-', alpha=0.3)
                    plt.text(i, first_two_days['True'].max() * 0.9, "白昼开始", rotation=90, alpha=0.7)
                else:
                    plt.axvline(x=i, color='navy', linestyle='-', alpha=0.3)
                    plt.text(i, first_two_days['True'].max() * 0.9, "白昼结束", rotation=90, alpha=0.7)
        
        plt.title(f"{title} (前两天)", fontsize=14)
        plt.xlabel('时间点', fontsize=12)
        plt.ylabel('功率 (MW)', fontsize=12)
        plt.legend(loc='best')
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.tight_layout()
        
        plt.savefig(save_path.replace('.png', '_2days.png'))
        plt.close()

def save_predictions_to_table(test_dates, test_times, y_true, predictions, is_daytime):
    """将预测结果保存为表2格式"""
    # 创建结果DataFrame
    result_table = pd.DataFrame({
        'date': np.repeat([d.strftime('%Y-%m-%d') for d in test_dates], 96),
        'time': np.tile([f"{h:02d}:{m:02d}" for h in range(24) for m in [0, 15, 30, 45]], len(test_dates)),
        'actual_power': y_true
    })
    
    # 添加预测值
    for model_name, y_pred in predictions.items():
        result_table[f'{model_name}_power'] = y_pred
        
    # 添加白天标记
    result_table['is_daytime'] = is_daytime
    
    # 保存表格
    result_table.to_csv(os.path.join(RESULTS_DIR, 'prediction_results_table2.csv'), index=False)
    
    # 另存一份只包含白天时段的结果
    result_table[result_table['is_daytime'] == 1].to_csv(
        os.path.join(RESULTS_DIR, 'prediction_results_daytime_only.csv'), index=False)
    
    return result_table

def fast_ensemble(predictions_dict):
    """
    快速集成多个模型的预测结果
    简单加权平均
    """
    print("执行快速模型集成...")
    
    # 设置各模型权重
    weights = {
        'Persistence': 0.2,
        'LightGBM': 0.5,
        'XGBoost': 0.3
    }
    
    # 计算加权平均
    ensemble_pred = np.zeros_like(list(predictions_dict.values())[0])
    for model, pred in predictions_dict.items():
        if model in weights:
            ensemble_pred += weights[model] * pred
    
    return ensemble_pred

# 主函数
if __name__ == "__main__":
    print("正在执行光伏电站日前发电功率快速预测方案...")
    
    # 指定使用站点1的数据
    station_id = 1
    
    # 加载数据
    start_time = time.time()
    df = load_station_data(station_id)
    print(f"成功加载站点{station_id}数据，共{len(df)}条记录")
    
    # 特征工程
    df_features = prepare_features(df)
    print(f"特征工程完成，共{df_features.shape[1]}个特征")
    
    # 划分训练集和测试集
    train_indices, test_indices = get_train_test_split(df_features)
    X_train = df_features.loc[train_indices]
    X_test = df_features.loc[test_indices]
    print(f"训练集大小: {len(X_train)}, 测试集大小: {len(X_test)}")
    
    # 获取测试集日期和时间
    test_dates = sorted(X_test['date'].unique())
    print(f"测试日期: {test_dates}")
    
    # 保存白昼标记，用于评估和可视化
    is_daytime = X_test['is_daytime'].values
    
    # 准备标签
    y_train = X_train['power'].values
    y_test = X_test['power'].values
    
    # 测试集时间点
    test_times = X_test['date_time']
    
    # 1. 运行Persistence模型（基准线）
    persistence_pred = persistence_model(X_train, test_dates)
    
    # 2. 运行快速LightGBM模型
    lgbm_pred, _ = fast_lightgbm_model(X_train, y_train, X_test)
    
    # 3. 运行快速XGBoost模型
    xgb_pred, _ = fast_xgboost_model(X_train, y_train, X_test)
    
    # 收集所有模型的结果
    predictions = {
        'Persistence': persistence_pred,
        'LightGBM': lgbm_pred,
        'XGBoost': xgb_pred
    }
    
    # 4. 快速集成
    ensemble_pred = fast_ensemble(predictions)
    predictions['Ensemble'] = ensemble_pred
    
    # 只评估白昼时段的模型性能
    daytime_results = evaluate_models(y_test, predictions, is_daytime)
    print("\n白昼时段模型评估结果:")
    print(daytime_results)
    
    # 保存评估结果
    daytime_results.to_csv(os.path.join(RESULTS_DIR, 'daytime_model_evaluation.csv'))
    
    # 绘制预测对比图
    plot_prediction_comparison(
        y_test,
        predictions,
        is_daytime,
        f"站点{station_id}预测效果对比(标记白昼时段)",
        os.path.join(RESULTS_DIR, 'prediction_comparison_with_daytime.png')
    )
    
    # 将预测结果保存为表2格式
    result_table = save_predictions_to_table(
        test_dates,
        test_times,
        y_test,
        predictions,
        is_daytime
    )
    
    total_time = time.time() - start_time
    print(f"快速预测方案完成！总耗时: {total_time:.2f}秒")
    print(f"结果已保存到 {RESULTS_DIR} 目录")
    
    # 保存总结果
    summary = pd.DataFrame({
        'Model': daytime_results.index,
        'MAE': daytime_results['MAE'],
        'nRMSE': daytime_results['nRMSE'],
        'SMAPE': daytime_results['SMAPE']
    })
    
    # 计算相对于Persistence基准的改进百分比
    base_mae = daytime_results.loc['Persistence', 'MAE']
    summary['MAE_Improvement(%)'] = (base_mae - summary['MAE']) / base_mae * 100
    
    print("\n模型性能总结 (白昼时段):")
    print(summary)
    summary.to_csv(os.path.join(RESULTS_DIR, 'model_summary.csv'), index=False) 