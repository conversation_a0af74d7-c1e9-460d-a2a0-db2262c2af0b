import os
import pandas as pd
import numpy as np
import matplotlib
# 设置非交互式后端，避免Qt错误
matplotlib.use('Agg')
import matplotlib.pyplot as plt
from matplotlib.font_manager import FontProperties
import seaborn as sns
from statsmodels.tsa.seasonal import STL
from scipy.fftpack import fft
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler
from datetime import datetime, timedelta
import matplotlib.dates as mdates
# 尝试导入地图绘制模块，如果不可用则跳过
try:
    import cartopy.crs as ccrs
    import cartopy.feature as cfeature
    HAS_CARTOPY = True
except ImportError:
    print("警告: 未安装cartopy包，将跳过地理地图绘制功能")
    HAS_CARTOPY = False
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
try:
    plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
    plt.rcParams['axes.unicode_minus'] = False    # 用来正常显示负号
except:
    print("无法设置中文字体，图表中文可能显示为方块")

# 创建结果目录
os.makedirs('Q1_results', exist_ok=True)

# 1. 读取数据
def load_data(station_id):
    file_path = f'dataset/station{station_id:02d}.csv'
    df = pd.read_csv(file_path)
    df['date_time'] = pd.to_datetime(df['date_time'])
    df['date'] = df['date_time'].dt.date
    df['hour'] = df['date_time'].dt.hour
    df['minute'] = df['date_time'].dt.minute
    df['time_of_day'] = df['hour'] + df['minute']/60
    df['month'] = df['date_time'].dt.month
    df['year'] = df['date_time'].dt.year
    df['day'] = df['date_time'].dt.day
    df['dayofyear'] = df['date_time'].dt.dayofyear
    return df

# 获取站点经纬度信息
def get_station_locations():
    # 这里应根据实际情况读取站点的经纬度信息
    # 示例数据，实际应用中应替换为真实数据
    locations = {
        0: {'name': '站点0', 'lat': 39.90, 'lon': 116.40},  # 示例坐标
        1: {'name': '站点1', 'lat': 31.23, 'lon': 121.47},
        2: {'name': '站点2', 'lat': 23.13, 'lon': 113.26},
        3: {'name': '站点3', 'lat': 30.58, 'lon': 114.31},
        4: {'name': '站点4', 'lat': 28.21, 'lon': 112.94},
        5: {'name': '站点5', 'lat': 22.54, 'lon': 114.06},
        6: {'name': '站点6', 'lat': 34.34, 'lon': 108.94},
        7: {'name': '站点7', 'lat': 36.06, 'lon': 120.38},
        8: {'name': '站点8', 'lat': 32.06, 'lon': 118.78},
        9: {'name': '站点9', 'lat': 30.25, 'lon': 120.17},
    }
    return locations

# 读取所有站点数据
stations_data = {}
for i in range(10):
    try:
        stations_data[i] = load_data(i)
        print(f"成功加载站点{i}数据，形状: {stations_data[i].shape}")
    except Exception as e:
        print(f"加载站点{i}数据失败: {e}")

# 2. 理论可发功率计算 (PVLib-clearsky简化实现)
def calculate_theoretical_power(df, capacity=None):
    """
    简化的理论功率计算，基于全球辐照度数据
    实际中可使用pvlib库进行更准确的计算
    """
    if capacity is None:
        # 估计容量：取最大功率的1.2倍作为装机容量估计
        capacity = df['power'].max() * 1.2
    
    # 使用NWP全球辐照度数据进行简单的功率估计
    # 理论功率 = 辐照度 * 容量 / 1000 * 效率因子
    efficiency = 0.85  # 假设一个系统效率
    df['theoretical_power'] = df['nwp_globalirrad'] * capacity / 1000 * efficiency
    
    # 计算相对偏差
    df['relative_efficiency'] = np.where(df['theoretical_power'] > 0, 
                                         df['power'] / df['theoretical_power'], 
                                         np.nan)
    return df

# 3. 长周期(季节性)分析
def seasonal_analysis(df, station_id):
    """季节性分析：月度聚合和趋势分析"""
    # 准备每日平均发电量数据
    daily_power = df.groupby('date')['power'].mean().reset_index()
    daily_power.set_index('date', inplace=True)
    daily_power.index = pd.to_datetime(daily_power.index)
    
    # 填充缺失值
    daily_power = daily_power.resample('D').mean()
    daily_power = daily_power.interpolate(method='linear')
    
    # 绘制时间序列图
    plt.figure(figsize=(14, 6))
    plt.plot(daily_power.index, daily_power, label='日均功率')
    plt.title(f'站点{station_id} - 每日平均发电功率时间序列')
    plt.xlabel('日期')
    plt.ylabel('功率 (MW)')
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.legend()
    plt.savefig(f'Q1_results/station{station_id}_daily_power.png', dpi=300)
    plt.close()
    
    # 计算移动平均作为趋势
    try:
        # 计算7天移动平均
        daily_power_ma7 = daily_power.rolling(window=7, center=True).mean()
        # 计算30天移动平均
        daily_power_ma30 = daily_power.rolling(window=30, center=True).mean()
        
        plt.figure(figsize=(14, 6))
        plt.plot(daily_power.index, daily_power, 'o-', alpha=0.3, label='原始数据')
        plt.plot(daily_power.index, daily_power_ma7, label='7天移动平均')
        plt.plot(daily_power.index, daily_power_ma30, label='30天移动平均')
        plt.title(f'站点{station_id} - 发电功率趋势分析')
        plt.xlabel('日期')
        plt.ylabel('功率 (MW)')
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.legend()
        plt.savefig(f'Q1_results/station{station_id}_trend_analysis.png', dpi=300)
        plt.close()
    except Exception as e:
        print(f"站点{station_id}的趋势分析失败: {e}")
    
    # 月度箱型图
    try:
        plt.figure(figsize=(12, 6))
        monthly_data = df.copy()
        monthly_data['month'] = monthly_data['date_time'].dt.month
        sns.boxplot(x='month', y='power', data=monthly_data)
        plt.title(f'站点{station_id} - 月度发电量分布')
        plt.xlabel('月份')
        plt.ylabel('功率 (MW)')
        plt.savefig(f'Q1_results/station{station_id}_monthly_boxplot.png', dpi=300)
        plt.close()
    except Exception as e:
        print(f"站点{station_id}的月度箱型图绘制失败: {e}")
    
    # Q-Q图
    try:
        plt.figure(figsize=(8, 8))
        from scipy import stats
        data_for_qq = daily_power.dropna().values.flatten()
        if len(data_for_qq) > 0:
            stats.probplot(data_for_qq, dist="norm", plot=plt)
            plt.title(f'站点{station_id} - 日均功率Q-Q图')
            plt.savefig(f'Q1_results/station{station_id}_qq_plot.png', dpi=300)
            plt.close()
    except Exception as e:
        print(f"站点{station_id}的Q-Q图绘制失败: {e}")
    
    # 按月计算平均发电量
    try:
        # 创建年月组合列
        df['yearmonth'] = df['year'].astype(str) + '-' + df['month'].astype(str).str.zfill(2)
        monthly_avg = df.groupby('yearmonth')['power'].mean().reset_index()
        monthly_avg.columns = ['yearmonth', 'avg_power']
    except Exception as e:
        print(f"站点{station_id}的月度平均计算失败: {e}")
        monthly_avg = pd.DataFrame({'yearmonth': [], 'avg_power': []})
    
    return monthly_avg

# 4. 短周期(日内)分析
def intraday_analysis(df, station_id):
    """日内功率曲线分析：聚类与典型日分析"""
    # 提取日内功率曲线
    # 假设数据是15分钟间隔，每天96个点
    daily_patterns = []
    dates = []
    
    # 找出完整的日数据
    for date, group in df.groupby('date'):
        if len(group) >= 96:  # 假设完整一天有96个数据点
            # 如果有超过96个点，只取前96个
            daily_pattern = group.sort_values('date_time').iloc[:96]['power'].values
            daily_patterns.append(daily_pattern)
            dates.append(date)
    
    if len(daily_patterns) > 0:
        # 转为数组
        daily_patterns = np.array(daily_patterns)
        
        # K-means聚类找出典型日模式
        scaler = StandardScaler()
        scaled_patterns = scaler.fit_transform(daily_patterns)
        
        # 确定最佳聚类数
        inertia = []
        K_range = range(1, min(11, len(daily_patterns) + 1))  # 最多10个聚类或数据量
        for k in K_range:
            kmeans = KMeans(n_clusters=k, random_state=42)
            kmeans.fit(scaled_patterns)
            inertia.append(kmeans.inertia_)
        
        # 绘制肘部图
        plt.figure(figsize=(10, 6))
        plt.plot(K_range, inertia, 'o-')
        plt.xlabel('聚类数量')
        plt.ylabel('惯性')
        plt.title(f'站点{station_id} - K-means聚类肘部图')
        plt.savefig(f'Q1_results/station{station_id}_kmeans_elbow.png', dpi=300)
        plt.close()
        
        # 选择适当的聚类数 (简化处理：选择3个聚类)
        k = min(3, len(daily_patterns))
        kmeans = KMeans(n_clusters=k, random_state=42)
        clusters = kmeans.fit_predict(scaled_patterns)
        
        # 绘制典型日图案
        plt.figure(figsize=(14, 8))
        
        # 创建具体时间点而不是简单的0-24小时范围
        reference_date = datetime(2023, 1, 1)
        time_points = [reference_date + timedelta(minutes=15*i) for i in range(96)]  # 假设96个点对应24小时
        
        for i in range(k):
            cluster_patterns = daily_patterns[clusters == i]
            if len(cluster_patterns) > 0:
                mean_pattern = np.mean(cluster_patterns, axis=0)
                plt.plot(time_points, mean_pattern, linewidth=2, label=f'聚类 {i+1} (n={len(cluster_patterns)})')
                
                # 计算分位数范围
                p10 = np.percentile(cluster_patterns, 10, axis=0)
                p90 = np.percentile(cluster_patterns, 90, axis=0)
                plt.fill_between(time_points, p10, p90, alpha=0.2)
        
        plt.xlabel('时间')
        plt.ylabel('功率 (MW)')
        plt.title(f'站点{station_id} - 典型日内功率曲线 (K={k})')
        
        # 设置x轴时间格式
        plt.gca().xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
        plt.gca().xaxis.set_major_locator(mdates.HourLocator(interval=2))
        
        plt.legend()
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.savefig(f'Q1_results/station{station_id}_typical_days.png', dpi=300)
        plt.close()
        
        # 计算功率变化率 (ramp rate)
        df['power_diff'] = df.groupby('date')['power'].diff()
        df['ramp_rate'] = df['power_diff'] / 0.25  # 假设15分钟间隔，转换为MW/h
        
        # 绘制ramp rate分布
        try:
            plt.figure(figsize=(10, 6))
            # 移除无穷大值和NaN并限制范围
            ramp_rate_clean = df['ramp_rate'].replace([np.inf, -np.inf], np.nan).dropna()
            # 使用matplotlib直接绘制直方图
            plt.hist(ramp_rate_clean, bins=50, alpha=0.7)
            plt.title(f'站点{station_id} - 功率变化率分布')
            plt.xlabel('功率变化率 (MW/h)')
            plt.ylabel('频率')
            plt.savefig(f'Q1_results/station{station_id}_ramp_rate.png', dpi=300)
            plt.close()
        except Exception as e:
            print(f"站点{station_id}的ramp rate图绘制失败: {e}")
        
        # 计算变异系数 (CV)
        daily_cv = []
        for date, group in df.groupby('date'):
            if len(group) > 0 and group['power'].mean() > 0:
                cv = group['power'].std() / group['power'].mean()
                if np.isfinite(cv):  # 检查CV是否为有限值
                    daily_cv.append(cv)
        
        if daily_cv:
            try:
                plt.figure(figsize=(10, 6))
                # 使用matplotlib直接绘制直方图
                plt.hist(daily_cv, bins=30, alpha=0.7)
                plt.title(f'站点{station_id} - 日内功率变异系数(CV)分布')
                plt.xlabel('变异系数')
                plt.ylabel('频率')
                plt.savefig(f'Q1_results/station{station_id}_cv.png', dpi=300)
                plt.close()
            except Exception as e:
                print(f"站点{station_id}的CV图绘制失败: {e}")
        
        return clusters, kmeans
    else:
        print(f"站点{station_id}没有足够的完整日数据进行分析")
        return None, None

# 5. 偏差诊断
def deviation_analysis(df, station_id):
    """分析实际功率与理论功率的偏差"""
    try:
        # 计算每日平均相对效率
        daily_rel_eff = df.groupby('date')['relative_efficiency'].mean().reset_index()
        daily_rel_eff['date'] = pd.to_datetime(daily_rel_eff['date'])
        
        # 绘制相对效率时间序列
        plt.figure(figsize=(14, 6))
        plt.plot(daily_rel_eff['date'], daily_rel_eff['relative_efficiency'])
        plt.axhline(y=1, color='r', linestyle='--')
        plt.title(f'站点{station_id} - 每日平均相对效率 (实际/理论)')
        plt.xlabel('日期')
        plt.ylabel('相对效率')
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.savefig(f'Q1_results/station{station_id}_daily_efficiency.png', dpi=300)
        plt.close()
        
        # 按月计算相对效率
        df['year_month'] = df['date_time'].dt.strftime('%Y-%m')
        monthly_rel_eff = df.groupby('year_month')['relative_efficiency'].mean().reset_index()
        
        plt.figure(figsize=(14, 6))
        plt.bar(monthly_rel_eff['year_month'], monthly_rel_eff['relative_efficiency'])
        plt.axhline(y=1, color='r', linestyle='--')
        plt.title(f'站点{station_id} - 月平均相对效率')
        plt.xlabel('年月')
        plt.ylabel('相对效率')
        plt.xticks(rotation=90)
        plt.tight_layout()
        plt.savefig(f'Q1_results/station{station_id}_monthly_efficiency.png', dpi=300)
        plt.close()
        
        # 按辐照度水平分析相对效率
        df['irrad_bin'] = pd.cut(df['nwp_globalirrad'], 
                                bins=[0, 100, 200, 300, 400, 500, 600, 700, 800, 900, 1000, np.inf],
                                labels=['0-100', '100-200', '200-300', '300-400', '400-500', 
                                        '500-600', '600-700', '700-800', '800-900', '900-1000', '1000+'])
        
        irrad_eff = df.groupby('irrad_bin')['relative_efficiency'].mean().reset_index()
        
        plt.figure(figsize=(12, 6))
        plt.bar(irrad_eff['irrad_bin'], irrad_eff['relative_efficiency'])
        plt.axhline(y=1, color='r', linestyle='--')
        plt.title(f'站点{station_id} - 不同辐照度水平下的相对效率')
        plt.xlabel('全球辐照度范围 (W/m²)')
        plt.ylabel('相对效率')
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig(f'Q1_results/station{station_id}_irradiance_efficiency.png', dpi=300)
        plt.close()
        
        return daily_rel_eff, monthly_rel_eff
    except Exception as e:
        print(f"站点{station_id}的偏差诊断失败: {e}")
        return None, None

# 6. 结论输出：定量总结发电特性
def output_summary(stations_data):
    """总结所有站点的发电特性"""
    results = []
    
    for station_id, df in stations_data.items():
        try:
            # 计算容量因子 (CF)
            capacity = df['power'].max() * 1.2  # 估计装机容量
            cf = df['power'].mean() / capacity if capacity > 0 else 0
            
            # 计算最大/最小日CF
            daily_power = df.groupby('date')['power'].mean()
            if len(daily_power) > 0:
                max_daily_cf = daily_power.max() / capacity if capacity > 0 else 0
                min_daily_cf = (daily_power[daily_power > 0].min() if len(daily_power[daily_power > 0]) > 0 else 0) / capacity if capacity > 0 else 0
                
                # 计算最大15分钟ramp
                df['power_diff'] = df.groupby('date')['power'].diff()
                max_ramp = df['power_diff'].abs().max()
                
                results.append({
                    'station_id': station_id,
                    'capacity_factor': cf,
                    'max_daily_cf': max_daily_cf,
                    'min_daily_cf': min_daily_cf,
                    'max_15min_ramp': max_ramp,
                    'max_15min_ramp_pct': max_ramp / capacity * 100 if capacity > 0 else 0,  # 转为百分比
                    'data_days': len(daily_power),
                    'capacity_estimate': capacity
                })
        except Exception as e:
            print(f"站点{station_id}的汇总统计失败: {e}")
    
    # 创建汇总DataFrame
    summary_df = pd.DataFrame(results)
    
    try:
        # 绘制容量因子比较图
        plt.figure(figsize=(12, 6))
        plt.bar(summary_df['station_id'], summary_df['capacity_factor'])
        plt.title('各站点容量因子(CF)比较')
        plt.xlabel('站点ID')
        plt.ylabel('容量因子')
        plt.xticks(summary_df['station_id'])
        plt.grid(axis='y', linestyle='--', alpha=0.7)
        plt.savefig('Q1_results/capacity_factor_comparison.png', dpi=300)
        plt.close()
        
        # 绘制最大ramp比较图
        plt.figure(figsize=(12, 6))
        plt.bar(summary_df['station_id'], summary_df['max_15min_ramp_pct'])
        plt.title('各站点最大15分钟功率变化率(占容量百分比)')
        plt.xlabel('站点ID')
        plt.ylabel('最大功率变化率(%)')
        plt.xticks(summary_df['station_id'])
        plt.grid(axis='y', linestyle='--', alpha=0.7)
        plt.savefig('Q1_results/max_ramp_comparison.png', dpi=300)
        plt.close()
        
        # 绘制站点地理分布图
        if HAS_CARTOPY:
            try:
                # 获取站点位置信息
                locations = get_station_locations()
                
                plt.figure(figsize=(12, 10))
                # 创建中国地图投影
                ax = plt.axes(projection=ccrs.PlateCarree())
                ax.add_feature(cfeature.COASTLINE)
                ax.add_feature(cfeature.BORDERS)
                ax.add_feature(cfeature.LAKES, alpha=0.5)
                ax.set_extent([70, 140, 15, 55])  # 设置中国地图范围
                
                # 绘制站点位置
                for station_id, info in locations.items():
                    if station_id in summary_df['station_id'].values:
                        station_data = summary_df[summary_df['station_id'] == station_id].iloc[0]
                        capacity = station_data['capacity_estimate']
                        cf = station_data['capacity_factor']
                        
                        plt.scatter(info['lon'], info['lat'], 
                                  s=capacity*3,  # 点大小与装机容量成正比
                                  c=[cf*100],   # 颜色与容量因子对应
                                  cmap='viridis',
                                  transform=ccrs.PlateCarree(),
                                  alpha=0.7)
                        
                        # 添加站点标签
                        plt.text(info['lon'], info['lat'], f"站点{station_id}", 
                                fontsize=9, ha='center', va='bottom', 
                                transform=ccrs.PlateCarree())
                
                # 添加颜色条和标题
                cbar = plt.colorbar(label='容量因子 (%)')
                plt.title('光伏电站地理分布与特性', fontsize=14)
                
                # 添加图例解释散点大小
                sizes = [10, 20, 30]
                labels = ['10MW', '20MW', '30MW']
                
                # 创建不显示的散点只用于图例
                handles = []
                for size in sizes:
                    handle = plt.scatter([], [], s=size*3, c='gray', alpha=0.7)
                    handles.append(handle)
                
                plt.legend(handles, labels, title='装机容量', loc='lower right')
                
                plt.savefig('Q1_results/stations_geographical_distribution.png', dpi=300)
                print("  站点地理分布图已保存")
            except Exception as e:
                print(f"绘制地理分布图失败: {e}")
        else:
            print("警告: 未安装cartopy包，将跳过地理地图绘制功能")
        
        # 保存汇总表
        summary_df.to_csv('Q1_results/stations_summary.csv', index=False)
        
        # 更新分析报告中的数值
        try:
            with open('Q1_results/分析报告.md', 'r', encoding='utf-8') as f:
                report = f.read()
            
            # 计算最大容量因子和最小容量因子之间的差异
            max_cf = summary_df['capacity_factor'].max()
            min_cf = summary_df['capacity_factor'].min()
            cf_diff_pct = (max_cf - min_cf) / min_cf * 100 if min_cf > 0 else 0
            
            # 获取最大ramp rate百分比
            max_ramp_pct = summary_df['max_15min_ramp_pct'].max()
            
            # 更新报告中的X%占位符
            report = report.replace('约X%', f'约{cf_diff_pct:.1f}%')
            report = report.replace('的X%', f'的{max_ramp_pct:.1f}%')
            
            with open('Q1_results/分析报告.md', 'w', encoding='utf-8') as f:
                f.write(report)
        except Exception as e:
            print(f"更新分析报告失败: {e}")
    except Exception as e:
        print(f"绘制汇总图表失败: {e}")
    
    return summary_df

# 主程序
def main():
    print("开始分析光伏电站发电特性...")
    
    # 对每个站点进行分析
    for station_id, df in stations_data.items():
        print(f"分析站点 {station_id}...")
        
        # 1. 理论可发功率计算
        df = calculate_theoretical_power(df)
        
        # 2. 长周期(季节性)分析
        seasonal_result = seasonal_analysis(df, station_id)
        
        # 3. 短周期(日内)分析
        intraday_result = intraday_analysis(df, station_id)
        
        # 4. 偏差诊断
        deviation_result = deviation_analysis(df, station_id)
    
    # 5. 输出总结
    summary = output_summary(stations_data)
    print("分析完成。结果保存在Q1_results目录中。")

if __name__ == "__main__":
    main() 